#!/bin/bash

echo "🚀 Enhanced Certified Data Seeder Script"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}Error: package.json not found. Please run this script from the pension-admin-api directory.${NC}"
    exit 1
fi

echo -e "${YELLOW}Step 1: Backing up existing database (optional but recommended)${NC}"
echo "Do you want to backup your current database before seeding? (y/n)"
read -r BACKUP_CHOICE

if [ "$BACKUP_CHOICE" = "y" ] || [ "$BACKUP_CHOICE" = "Y" ]; then
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}Creating backup with timestamp: $TIMESTAMP${NC}"
    npx prisma db pull
    cp prisma/schema.prisma "prisma/backups/schema_backup_$TIMESTAMP.prisma" 2>/dev/null || mkdir -p prisma/backups && cp prisma/schema.prisma "prisma/backups/schema_backup_$TIMESTAMP.prisma"
    echo -e "${GREEN}✓ Backup created${NC}"
fi

echo ""
echo -e "${YELLOW}Step 2: Running enhanced seeder${NC}"
echo "This will create:"
echo "  • 1000 participants"
echo "  • 3000 certified data records (3 years each)"
echo "  • All certified data tables including:"
echo "    - CertifiedPensionCorrectionsStartOfYear"
echo "    - CertifiedPensionCorrectionsEndOfYear"
echo "    - CertifiedPensionsAccrual"
echo ""
echo "Continue? (y/n)"
read -r CONTINUE_CHOICE

if [ "$CONTINUE_CHOICE" != "y" ] && [ "$CONTINUE_CHOICE" != "Y" ]; then
    echo "Seeding cancelled."
    exit 0
fi

echo -e "${GREEN}Running enhanced seeder...${NC}"
npx ts-node prisma/seeders/certified-data-enhanced.seeder.ts

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Seeding completed successfully!${NC}"
    echo ""
    echo -e "${YELLOW}IMPORTANT: Frontend Pagination Fix Required${NC}"
    echo "=========================================="
    echo ""
    echo "To see all 1000 participants in the frontend, you need to fix the pagination limit:"
    echo ""
    echo -e "${YELLOW}File to edit:${NC}"
    echo "  pension-admin-frontend/src/api/graphHooks/useCertificationManagementGraph.ts"
    echo ""
    echo -e "${YELLOW}Line 23 - Change from:${NC}"
    echo "    take: 25,"
    echo ""
    echo -e "${YELLOW}To:${NC}"
    echo "    take: 1000,  // Or implement proper pagination with load more button"
    echo ""
    echo -e "${GREEN}Alternative solution:${NC} Implement proper pagination with:"
    echo "  • Load more button"
    echo "  • Infinite scroll"
    echo "  • Page size selector (25, 50, 100, 500, 1000)"
    echo ""
    echo -e "${YELLOW}After making the change:${NC}"
    echo "  1. Save the file"
    echo "  2. The frontend will hot-reload"
    echo "  3. Navigate to Certification Management page"
    echo "  4. You should now see all 1000 participants"
    echo ""
else
    echo ""
    echo -e "${RED}❌ Seeding failed. Please check the error messages above.${NC}"
    echo ""
    echo "Common issues:"
    echo "  • Database connection issues"
    echo "  • Prisma client not generated (run: npx prisma generate)"
    echo "  • Missing dependencies (run: npm install)"
    exit 1
fi
