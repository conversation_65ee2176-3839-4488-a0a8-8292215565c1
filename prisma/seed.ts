import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
    // Seed Roles
    const roles = [
        {
            id: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
            name: 'Admin',
            description: 'System Admin',
        },
        {
            id: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e',
            name: 'Reviewer',
            description: 'Reviewer',
        },
        {
            id: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8',
            name: 'Accountant',
            description: 'Accountant',
        },
        {
            id: '7da8cfe0-de11-414b-89f0-a03c5ce366de',
            name: 'Editor',
            description: 'Editor',
        },
    ]

    for (const role of roles) {
        await prisma.role.upsert({
            where: { id: role.id },
            update: {},
            create: role,
        })
    }

    // Seed Users
    const users = [
        {
            id: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            firebaseUid: 'CSpw5IKuoERVDyD9jSIF6XOfedX2',
            email: '<EMAIL>',
            firstname: 'test',
            lastname: 'editor',
            roleId: '7da8cfe0-de11-414b-89f0-a03c5ce366de',
        },
        {
            id: 'be196c0f-ee7b-42a3-9163-885f649e65ef',
            firebaseUid: 'bwzgkNU2SSSCA1HzoKJVJdI7mqL2',
            email: '<EMAIL>',
            firstname: 'Admin',
            lastname: 'Admin',
            roleId: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
        },
        {
            id: '24c4a88c-123c-42d3-b24e-693c51aca1ed',
            firebaseUid: 'MOZfw8bnz2UEPnKUPfRE3Od1BJt2',
            email: '<EMAIL>',
            firstname: 'Super',
            lastname: 'Admin',
            roleId: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
        },
        {
            id: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
            firebaseUid: '1EPxoLbe3maU6x4DC40J0icDAkI2',
            email: '<EMAIL>',
            firstname: 'Accountant',
            lastname: 'Accountant',
            roleId: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8',
        },
        {
            id: '82920d1b-c3b1-4299-9581-086cddf21615',
            firebaseUid: 'e5BM87KF00OEwlKFozMD1KMSCAY2',
            email: '<EMAIL>',
            firstname: 'Reviewer',
            lastname: 'Reviewer',
            roleId: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e',
        },
    ]

    for (const user of users) {
        await prisma.user.upsert({
            where: { id: user.id },
            update: {},
            create: user,
        })
    }

    // Create a participant with all related data
    const participant = await prisma.participant.create({
        data: {
            createdBy: 'be196c0f-ee7b-42a3-9163-885f649e65ef', // Admin user
            updatedBy: 'be196c0f-ee7b-42a3-9163-885f649e65ef', // Admin user
            personalInfo: {
                create: {
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    phone: '************',
                    maritalStatus: 'Married',
                    sex: 'Male',
                    birthDay: 1,
                    birthMonth: 1,
                    birthYear: 1980,
                    address: {
                        create: {
                            street: '123 Main St',
                            houseNumber: 'Apt 4B',
                            postalCode: '12345',
                            city: 'Anytown',
                            state: 'CA',
                            country: 'USA',
                        },
                    },
                    partnerInfo: {
                        create: [
                            {
                                isCurrent: true,
                                firstName: 'Jane',
                                lastName: 'Doe',
                                dateOfBirth: new Date('1982-02-20'),
                                isDeceased: false,
                                startDate: new Date('2005-06-15'),
                            },
                        ],
                    },
                    children: {
                        create: [
                            {
                                firstName: 'Junior',
                                lastName: 'Doe',
                                dateOfBirth: new Date('2010-08-25'),
                                isOrphan: false,
                                isStudying: true,
                            },
                        ],
                    },
                },
            },
            employmentInfo: {
                create: {
                    employeeId: 'EMP123',
                    department: 'Engineering',
                    position: 'Software Engineer',
                    regNum: 12345,
                    havNum: 67890,
                    startDate: new Date('2015-01-10'),
                    status: 'Active',
                    salaryEntries: {
                        create: [
                            {
                                year: 2023,
                                amount: 100000,
                                partTimePercentage: 1.0,
                            },
                            {
                                year: 2024,
                                amount: 105000,
                                partTimePercentage: 1.0,
                            },
                        ],
                    },
                },
            },
            documents: {
                create: [
                    {
                        name: 'Contract.pdf',
                        type: 'PDF',
                        path: '/documents/contract.pdf',
                    },
                ],
            },
        },
        include: {
            personalInfo: {
                include: {
                    address: true,
                    partnerInfo: true,
                    children: true,
                },
            },
            employmentInfo: {
                include: {
                    salaryEntries: true,
                },
            },
            pensionInfo: true,
            documents: true,
        },
    })
}
//
// main()
//     .catch((e) => {
//         console.error(e)
//         process.exit(1)
//     })
//     .finally(async () => {
//         await prisma.$disconnect()
//     })
