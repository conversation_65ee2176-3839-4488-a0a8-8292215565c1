import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Start seeding demo data...');

  // Roles
  await prisma.role.upsert({
    where: { id: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f' },
    update: {},
    create: { id: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f', name: 'Admin', description: 'System Admin' },
  });
  await prisma.role.upsert({
    where: { id: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e' },
    update: {},
    create: { id: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e', name: 'Reviewer', description: 'Reviewer' },
  });
  await prisma.role.upsert({
    where: { id: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8' },
    update: {},
    create: { id: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8', name: 'Accountant', description: 'Accountant' },
  });
  await prisma.role.upsert({
    where: { id: '7da8cfe0-de11-414b-89f0-a03c5ce366de' },
    update: {},
    create: { id: '7da8cfe0-de11-414b-89f0-a03c5ce366de', name: 'Editor', description: 'Editor' },
  });

  // Users
  await prisma.user.upsert({
    where: { id: 'abcedefa-25bf-4f85-8bd3-dea0397a489d' },
    update: {},
    create: {
      id: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      firebaseUid: 'CSpw5IKuoERVDyD9jSIF6XOfedX2',
      email: '<EMAIL>',
      firstname: 'test',
      lastname: 'editor',
      roleId: '7da8cfe0-de11-414b-89f0-a03c5ce366de',
    },
  });
  await prisma.user.upsert({
    where: { id: 'be196c0f-ee7b-42a3-9163-885f649e65ef' },
    update: {},
    create: {
      id: 'be196c0f-ee7b-42a3-9163-885f649e65ef',
      firebaseUid: 'bwzgkNU2SSSCA1HzoKJVJdI7mqL2',
      email: '<EMAIL>',
      firstname: 'Admin',
      lastname: 'Admin',
      roleId: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
    },
  });
  await prisma.user.upsert({
    where: { id: '24c4a88c-123c-42d3-b24e-693c51aca1ed' },
    update: {},
    create: {
      id: '24c4a88c-123c-42d3-b24e-693c51aca1ed',
      firebaseUid: 'MOZfw8bnz2UEPnKUPfRE3Od1BJt2',
      email: '<EMAIL>',
      firstname: 'Super',
      lastname: 'Admin',
      roleId: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
    },
  });
  await prisma.user.upsert({
    where: { id: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38' },
    update: {},
    create: {
      id: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
      firebaseUid: '1EPxoLbe3maU6x4DC40J0icDAkI2',
      email: '<EMAIL>',
      firstname: 'Accountant',
      lastname: 'Accountant',
      roleId: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8',
    },
  });
  await prisma.user.upsert({
    where: { id: '82920d1b-c3b1-4299-9581-086cddf21615' },
    update: {},
    create: {
      id: '82920d1b-c3b1-4299-9581-086cddf21615',
      firebaseUid: 'e5BM87KF00OEwlKFozMD1KMSCAY2',
      email: '<EMAIL>',
      firstname: 'Reviewer',
      lastname: 'Reviewer',
      roleId: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e',
    },
  });

  await prisma.pensionParameters.upsert({
    where: { id: '6d7bf7ad-1594-48ca-918b-f49d06cda067' },
    update: {},
    create: {
      id: '6d7bf7ad-1594-48ca-918b-f49d06cda067',
      accrualPercentage: 2,
      annualMultiplier: 13,
      offsetAmount: 17616,
      partnersPensionPercentage: 70,
      retirementAge: 65,
      voluntaryContributionInterestRate: 0.04,
      minimumPensionBase: 25000,
      year: '2025',
      effectiveDate: '2025-08-14T00:00:00.000Z',
      userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      indexationPercentage: 3,
    },
  });
  await prisma.pensionParameters.upsert({
    where: { id: 'ffadbb8c-19bb-4cb0-a61d-d30cbd3ab4b1' },
    update: {},
    create: {
      id: 'ffadbb8c-19bb-4cb0-a61d-d30cbd3ab4b1',
      accrualPercentage: 2,
      annualMultiplier: 13,
      offsetAmount: 17616,
      partnersPensionPercentage: 70,
      retirementAge: 65,
      voluntaryContributionInterestRate: 0.04,
      minimumPensionBase: 25000,
      year: '2022',
      effectiveDate: '2025-08-14T00:00:00.000Z',
      userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      indexationPercentage: 3,
    },
  });
  await prisma.pensionParameters.upsert({
    where: { id: '0fe4c317-7308-4fd1-b003-ac1496b53148' },
    update: {},
    create: {
      id: '0fe4c317-7308-4fd1-b003-ac1496b53148',
      accrualPercentage: 2,
      annualMultiplier: 13,
      offsetAmount: 17616,
      partnersPensionPercentage: 70,
      retirementAge: 65,
      voluntaryContributionInterestRate: 0.04,
      minimumPensionBase: 25000,
      year: '2020',
      effectiveDate: '2025-08-14T00:00:00.000Z',
      userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      indexationPercentage: 3,
    },
  });
  await prisma.pensionParameters.upsert({
    where: { id: 'f1d6e5a3-8f82-438b-9c8f-efbc9f06b19d' },
    update: {},
    create: {
      id: 'f1d6e5a3-8f82-438b-9c8f-efbc9f06b19d',
      accrualPercentage: 2,
      annualMultiplier: 13,
      offsetAmount: 17616,
      partnersPensionPercentage: 70,
      retirementAge: 65,
      voluntaryContributionInterestRate: 0.04,
      minimumPensionBase: 25000,
      year: '2023',
      effectiveDate: '2025-08-14T00:00:00.000Z',
      userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      indexationPercentage: 3,
    },
  });
  await prisma.pensionParameters.upsert({
    where: { id: 'f2654f95-4a68-41fe-bab8-4059a7b8e2a2' },
    update: {},
    create: {
      id: 'f2654f95-4a68-41fe-bab8-4059a7b8e2a2',
      accrualPercentage: 2,
      annualMultiplier: 13,
      offsetAmount: 17616,
      partnersPensionPercentage: 70,
      retirementAge: 65,
      voluntaryContributionInterestRate: 0.04,
      minimumPensionBase: 25000,
      year: '2024',
      effectiveDate: '2025-08-14T00:00:00.000Z',
      userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      indexationPercentage: 3,
    },
  });
  await prisma.pensionParameters.upsert({
    where: { id: '486cf38a-6e3d-45c4-ba51-1b0de456b297' },
    update: {},
    create: {
      id: '486cf38a-6e3d-45c4-ba51-1b0de456b297',
      accrualPercentage: 2,
      annualMultiplier: 13,
      offsetAmount: 17616,
      partnersPensionPercentage: 70,
      retirementAge: 65,
      voluntaryContributionInterestRate: 0.04,
      minimumPensionBase: 25000,
      year: '2021',
      effectiveDate: '2025-08-14T00:00:00.000Z',
      userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      indexationPercentage: 3,
    },
  });

  // Participant: aaaac6c3-0177-4306-b57c-a50ad301b04f
  await prisma.participant.upsert({
    where: { id: 'aaaac6c3-0177-4306-b57c-a50ad301b04f' },
    update: {},
    create: {
      id: 'aaaac6c3-0177-4306-b57c-a50ad301b04f',
      createdAt: '2025-08-14T11:12:55.672Z',
      createdBy: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      approvalStatus: 'APPROVED',
      updatedAt: '2025-09-11T19:32:09.608Z',
      updatedBy: '82920d1b-c3b1-4299-9581-086cddf21615',
      lastModified: '2025-09-11T19:32:09.607Z',
      personalInfo: {
        create: {
          id: 'd28bd5da-35e8-4f51-83b1-eed5f3aceaec',
          firstName: 'Alfredo',
          lastName: 'Bonaventure',
          sex: 'Male',
          email: '<EMAIL>',
          phone: '2342342',
          maritalStatus: 'Single',
          birthDay: 16,
          birthMonth: 8,
          birthYear: 1985,
        },
      },
      employmentInfo: {
        create: {
          id: '6d38b55b-96cd-4843-86d7-c6cbc9b75105',
          employeeId: 'E343',
          department: 'asdf asdfa',
          position: 'AShoma',
          startDate: '2025-08-22T00:00:00.000Z',
          status: 'Active',
          salaryEntries: {
            create: {
              id: '5353ce9a-dcb5-48ee-82de-6e46ef42c47b',
              year: 2025,
              amount: 4599,
              partTimePercentage: 100,
            },
          },
        },
      },
      pensionInfo: {
        create: {
          id: 'f329de6a-74cf-4df3-8f21-aef3cfeb7ec1',
          code: 10,
          codeEffectiveDate: '2025-08-14T11:11:25.136Z',
          codeDescription: 'Active',
        },
      },
    },
  });

  // Participant: 123778ae-f191-4172-b42f-10af4868e666
  await prisma.participant.upsert({
    where: { id: '123778ae-f191-4172-b42f-10af4868e666' },
    update: {},
    create: {
      id: '123778ae-f191-4172-b42f-10af4868e666',
      createdAt: '2025-08-11T18:24:22.400Z',
      createdBy: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      approvalStatus: 'APPROVED',
      updatedAt: '2025-08-11T18:25:39.661Z',
      updatedBy: '82920d1b-c3b1-4299-9581-086cddf21615',
      lastModified: '2025-08-11T18:25:39.660Z',
      personalInfo: {
        create: {
          id: '88f65736-ddf5-443e-b538-f97f16ec9665',
          firstName: 'Benjamin',
          lastName: 'Bengola',
          sex: 'Male',
          email: '<EMAIL>',
          phone: '+************',
          maritalStatus: 'Married',
          birthDay: 26,
          birthMonth: 11,
          birthYear: 1984,
          address: {
            create: {
              id: '53b49641-2885-46b9-b506-5fe7ffcc0e91',
              street: 'Main Street',
              houseNumber: '123',
              postalCode: '12345',
              city: 'Oranjestad',
              state: 'Aruba',
              country: 'Aruba',
            },
          },
        },
      },
      employmentInfo: {
        create: {
          id: '123778ae-f191-4172-b42f-10af4868e666',
          employeeId: 'EMP001',
          department: 'IT Department',
          position: 'Software Developer',
          regNum: 841101,
          startDate: '2007-05-01T00:00:00.000Z',
          endDate: '2025-08-26T00:00:00.000Z',
          status: 'Active',
          salaryEntries: {
            create: [
              {
                id: '98221bc3-cd73-417a-8d12-bb8fee51f4bf',
                year: 2023,
                amount: 4555,
                partTimePercentage: 100,
              },
              {
                id: '08ca4a6b-56a4-4e22-9cb8-cbd46de050e4',
                year: 2024,
                amount: 4680,
                partTimePercentage: 100,
              },
              {
                id: 'df2ec91f-f3af-42f6-a836-20bcb11bf1b7',
                year: 2025,
                amount: 4810,
                partTimePercentage: 100,
              },
            ],
          },
        },
      },
      pensionInfo: {
        create: {
          id: 'fe694ed4-987e-466d-8793-d0e83622f1ee',
          code: 11,
          previousCode: 30,
          codeEffectiveDate: '2025-08-26T00:00:00.000Z',
          previousCodeEffectiveDate: '2025-08-26T00:00:00.000Z',
          codeDescription: 'Disabled',
          codeImpact: 'InActive service but continued pension accrual and indexation',
          accruedGrossAnnualOldAgePension: 99,
          accruedGrossAnnualPartnersPension: 99,
          accruedGrossAnnualSinglesPension: 99,
          attainableGrossAnnualOldAgePension: 99,
          extraAccruedGrossAnnualOldAgePension: 99,
          extraAccruedGrossAnnualPartnersPension: 99,
          grossAnnualDisabilityPension: 99,
          pensionBase: 9999,
          accrualStartDate: '2025-08-26T18:29:03.159Z',
        },
      },
      certifiedData: {
        create: [
          {
            id: 'b25da401-0d48-4be8-9ccd-14b3dcd86862',
            certificationYear: 2022,
            certificationStatus: 'completed',
            certifiedAt: '2025-08-11T18:39:20.000Z',
            certifiedById: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
            certifiedPensionInfo: {
              create: {
                id: 'ea7cd4b0-ac5f-42b7-a2b5-0028a0bb4e97',
                code: 10,
                codeDescription: 'asdfasd',
                accruedGrossAnnualOldAgePension: 11515.98,
                accruedGrossAnnualPartnersPension: 8061.39,
                accruedGrossAnnualSinglesPension: 343,
                attainableGrossAnnualOldAgePension: 3434,
                extraAccruedGrossAnnualOldAgePension: 33,
                extraAccruedGrossAnnualPartnersPension: 33,
                grossAnnualDisabilityPension: 0,
                pensionBase: 3333,
                accrualEndDate: '2022-12-31T08:14:47.000Z',
                accrualStartDate: '2022-01-01T08:10:54.000Z',
              },
            },
            certifiedPensionParameters: {
              create: {
                id: 'ffadbb8c-19bb-4cb0-a61d-d30cbd3ab4b1',
                accrualPercentage: 13,
                annualMultiplier: 17616,
                offsetAmount: 3,
                partnersPensionPercentage: 65,
                retirementAge: 0,
                voluntaryContributionInterestRate: 25000,
                minimumPensionBase: 2022,
                year: '2022',
                effectiveDate: '2025-08-11T18:49:06.356Z',
              },
            },
            certifiedPensionsAccrual: {
              create: {
                id: '025ad4ce-a8b6-4e50-bb51-69e5e0f123ce',
                pensionBase: 31674.43,
                opteAccrualAfterReferenceDate: 34,
                opteAccrualToReferenceDate: 34,
                wpteAccrualToReferenceDate: 34,
              },
            },
            certifiedPersonalInfo: {
              create: {
                id: 'bdafcfca-3f50-4988-93ad-93aa456c54bf',
                firstName: 'James ',
                lastName: 'Gunn',
              },
            },
          },
          {
            id: 'f030066f-cf8d-48da-b886-436efc6ab929',
            certificationYear: 2023,
            certificationStatus: 'completed',
            certifiedAt: '2025-08-11T18:39:20.000Z',
            certifiedById: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
            certifiedChild: {
              create: {
                id: 'e64010a0-746c-4f93-b275-ec3ce1a20326',
                firstName: 'asdf asd',
                lastName: 'asdfasd',
                dateOfBirth: '2025-08-11T18:45:33.000Z',
                isOrphan: false,
                isStudying: false,
              },
            },
            certifiedEmploymentInfo: {
              create: {
                id: 'c2ae98af-e38f-4652-9d31-ac850c9b3aac',
                employeeId: 'EMP33',
                department: 'Deep Well',
                position: 'Goody',
                regNum: 2323,
                havNum: 2,
                startDate: '2000-08-11T18:44:48.000Z',
                status: 'Active',
                certifiedSalaryEntries: {
                  create: {
                    id: '9add3303-6228-4245-b08b-9835be3e5ab6',
                    year: 2023,
                    amount: 4555,
                    partTimePercentage: 78,
                  },
                },
              },
            },
            certifiedIndexationStartOfYear: {
              create: {
                id: '3860eda5-064c-4e1c-ae6c-6d9c003f576d',
                accruedGrossAnnualOldAgePension: 94.87,
                accruedGrossAnnualPartnersPension: 66.41,
                accruedGrossAnnualSinglesPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                grossAnnualDisabilityPension: 0,
              },
            },
            certifiedPensionCorrections: {
              create: {
                id: 'de1efae7-1dab-4849-8311-0fea2c61be28',
                accruedGrossAnnualOldAgePension: 3,
                attainableGrossAnnualOldAgePension: 3,
                accruedGrossAnnualPartnersPension: 3,
                accruedGrossAnnualSinglesPension: 3,
                grossAnnualDisabilityPension: 3,
                extraAccruedGrossAnnualOldAgePension: 3,
                extraAccruedGrossAnnualPartnersPension: 3,
                correction: 3,
                year: '2020',
              },
            },
            certifiedPensionCorrectionsEndOfYear: {
              create: {
                id: '0746b1cf-93ed-4f79-ae45-20aff6b7ce75',
                accruedGrossAnnualOldAgePension: 10373.08,
                attainableGrossAnnualOldAgePension: 33080,
                accruedGrossAnnualPartnersPension: 7913,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                correction: 2,
                year: '2023',
              },
            },
            certifiedPensionCorrectionsStartOfYear: {
              create: {
                id: '126ba904-8eb2-46b6-83c8-cc4bd6f7a9bd',
                accruedGrossAnnualOldAgePension: 0,
                attainableGrossAnnualOldAgePension: 0,
                accruedGrossAnnualPartnersPension: 0,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                correction: 0,
                year: '2023',
              },
            },
            certifiedPensionInfo: {
              create: {
                id: '442e6a7e-8c4b-4c19-a7ef-ab2e2e2b1159',
                code: 10,
                codeDescription: 'asdfasd',
                accruedGrossAnnualOldAgePension: 11515.98,
                accruedGrossAnnualPartnersPension: 8061.39,
                attainableGrossAnnualOldAgePension: 33080,
                extraAccruedGrossAnnualOldAgePension: 33,
                extraAccruedGrossAnnualPartnersPension: 33,
                grossAnnualDisabilityPension: 0,
                pensionBase: 3333,
                accrualEndDate: '2023-12-31T08:13:43.000Z',
                accrualStartDate: '2023-01-01T08:11:00.000Z',
              },
            },
            certifiedPensionParameters: {
              create: {
                id: 'f1d6e5a3-8f82-438b-9c8f-efbc9f06b19d',
                accrualPercentage: 13,
                annualMultiplier: 17616,
                offsetAmount: 3,
                partnersPensionPercentage: 65,
                retirementAge: 0,
                voluntaryContributionInterestRate: 25000,
                minimumPensionBase: 2023,
                year: '2023',
                effectiveDate: '2025-08-11T18:49:06.356Z',
              },
            },
            certifiedPensionsAccrual: {
              create: {
                id: '0746b1cf-93ed-4f79-ae45-20aff6b7ce75',
                pensionBase: 41599,
                opteAccrualAfterReferenceDate: 21564.46,
                opteAccrualToReferenceDate: 831.98,
                wpteAccrualToReferenceDate: 24.96,
              },
            },
            certifiedPersonalInfo: {
              create: {
                id: '0a555e7c-91d0-470d-900f-cacb66721c3e',
                firstName: 'Jonls',
                lastName: 'Bengola',
              },
            },
          },
          {
            id: '5c839ae7-10e6-4d38-ba0b-ea88f49ac5d4',
            certificationYear: 2024,
            certificationStatus: 'pending',
            certifiedAt: '2025-08-11T18:39:20.000Z',
            certifiedById: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
            certifiedChild: {
              create: {
                id: 'b3db9990-8533-48f5-b48c-dea46f6688ab',
                firstName: 'asdf asd',
                lastName: 'asdfasd',
                dateOfBirth: '2025-08-11T18:45:33.000Z',
                isOrphan: false,
                isStudying: false,
              },
            },
            certifiedEmploymentInfo: {
              create: {
                id: '10c09870-ab8b-46a7-b63d-dbcb813a7bdc',
                employeeId: 'EMP33',
                department: 'Deep Well',
                position: 'Goody',
                regNum: 2323,
                havNum: 2,
                startDate: '2000-08-11T18:44:48.000Z',
                status: 'Active',
                certifiedSalaryEntries: {
                  create: {
                    id: 'd26ca573-5d5e-481c-9ddb-c600c1c49e42',
                    year: 2024,
                    amount: 4680,
                    partTimePercentage: 87,
                  },
                },
              },
            },
            certifiedIndexationStartOfYear: {
              create: {
                id: '18b4277f-0f6d-412b-a525-e203645bf9ef',
                accruedGrossAnnualOldAgePension: 3005.4,
                accruedGrossAnnualPartnersPension: 241.84,
                accruedGrossAnnualSinglesPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                grossAnnualDisabilityPension: 0,
              },
            },
            certifiedPensionCorrections: {
              create: {
                id: '120bd63c-92ae-465a-ba85-bb27810b3a23',
                accruedGrossAnnualOldAgePension: 3,
                attainableGrossAnnualOldAgePension: 3,
                accruedGrossAnnualPartnersPension: 3,
                accruedGrossAnnualSinglesPension: 3,
                grossAnnualDisabilityPension: 3,
                extraAccruedGrossAnnualOldAgePension: 3,
                extraAccruedGrossAnnualPartnersPension: 3,
                correction: 3,
                year: '2020',
              },
            },
            certifiedPensionCorrectionsEndOfYear: {
              create: {
                id: '130b2b52-7dfc-4bab-a003-055c52061345',
                accruedGrossAnnualOldAgePension: 11515.98,
                attainableGrossAnnualOldAgePension: 34268,
                accruedGrossAnnualPartnersPension: 8061.39,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 2,
                correction: 2,
                year: '2024',
              },
            },
            certifiedPensionCorrectionsStartOfYear: {
              create: {
                id: '7765ba29-9054-45a9-9402-f441d6f927ba',
                accruedGrossAnnualOldAgePension: 0,
                attainableGrossAnnualOldAgePension: 0,
                accruedGrossAnnualPartnersPension: 0,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                correction: 0,
                year: '2024',
              },
            },
            certifiedPensionInfo: {
              create: {
                id: 'ea229230-c375-4161-954b-ff84886148fd',
                code: 10,
                codeDescription: 'asdfasd',
                accruedGrossAnnualOldAgePension: 12725.94,
                accruedGrossAnnualPartnersPension: 8908.36,
                accruedGrossAnnualSinglesPension: 0,
                attainableGrossAnnualOldAgePension: 34268,
                extraAccruedGrossAnnualOldAgePension: 44,
                extraAccruedGrossAnnualPartnersPension: 44,
                grossAnnualDisabilityPension: 0,
                pensionBase: 4444,
                accrualEndDate: '2024-12-31T08:12:49.000Z',
                accrualStartDate: '2024-01-01T08:10:44.000Z',
              },
            },
            certifiedPensionParameters: {
              create: {
                id: 'f2654f95-4a68-41fe-bab8-4059a7b8e2a2',
                accrualPercentage: 13,
                annualMultiplier: 17616,
                offsetAmount: 3,
                partnersPensionPercentage: 65,
                retirementAge: 0,
                voluntaryContributionInterestRate: 25000,
                minimumPensionBase: 2024,
                year: '2024',
                effectiveDate: '2025-08-11T18:49:06.356Z',
              },
            },
            certifiedPensionsAccrual: {
              create: {
                id: '130b2b52-7dfc-4bab-a003-055c52061345',
                pensionBase: 43224,
                opteAccrualAfterReferenceDate: 21542.36,
                opteAccrualToReferenceDate: 864.48,
                wpteAccrualToReferenceDate: 25.93,
              },
            },
            certifiedPersonalInfo: {
              create: {
                id: '49e3266b-d2a6-48a3-bcb3-700c6c3d3949',
                firstName: 'Benjamin',
                lastName: 'Bengola',
              },
            },
          },
        ],
      },
    },
  });

  // Participant: 6fa9cc1d-0811-44b5-b48c-475cdceed461
  await prisma.participant.upsert({
    where: { id: '6fa9cc1d-0811-44b5-b48c-475cdceed461' },
    update: {},
    create: {
      id: '6fa9cc1d-0811-44b5-b48c-475cdceed461',
      createdAt: '2025-08-13T18:09:27.409Z',
      createdBy: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      status: 'active',
      approvalStatus: 'APPROVED',
      updatedAt: '2025-08-13T18:09:27.409Z',
      updatedBy: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
      personalInfo: {
        create: {
          id: 'd8d8bda3-7b10-4877-82cc-79fb145dc382',
          firstName: 'Johnioloo',
          lastName: 'adsf asd',
          sex: 'Male',
          email: '<EMAIL>',
          phone: '+************',
          maritalStatus: 'Married',
          birthDay: 15,
          birthMonth: 1,
          birthYear: 1990,
          address: {
            create: {
              id: 'd9640b4d-c865-4534-825b-0ebd51a939d6',
              street: 'Accra',
              houseNumber: 'H98',
              postalCode: '23321',
              city: 'Madina',
              state: 'Ghana',
              country: 'Ghana',
            },
          },
        },
      },
      employmentInfo: {
        create: {
          id: '453e5b76-6ff9-4c2b-bbdc-3e6c2ab10e91',
          employeeId: 'EMP001',
          department: 'IT Department',
          position: 'Software Developer',
          startDate: '2020-01-01T00:00:00.000Z',
          status: 'Active',
          salaryEntries: {
            create: [
              {
                id: '1d0610ce-ade5-42f0-97fe-32045e061861',
                year: 2020,
                amount: 5000,
                partTimePercentage: 100,
              },
              {
                id: '5602f6da-5bbc-45eb-a85e-49299ce890dd',
                year: 2021,
                amount: 5500,
                partTimePercentage: 100,
              },
            ],
          },
        },
      },
      pensionInfo: {
        create: {
          id: '7eb5a36a-7c16-48d9-b4e7-190d0380c596',
          code: 10,
          codeEffectiveDate: '2020-01-01T00:00:00.000Z',
          codeDescription: 'Active',
        },
      },
      certifiedData: {
        create: [
          {
            id: 'f871c6ed-8d37-4197-8ddc-fff816545832',
            certificationYear: 2023,
            certificationStatus: 'completed',
            certifiedAt: '2025-08-11T18:39:20.000Z',
            certifiedById: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
            certifiedEmploymentInfo: {
              create: {
                id: 'f4622fd7-7150-4aef-a0b9-bbc68259995b',
                employeeId: 'EMP3322',
                department: 'Deep Well',
                position: 'Goody',
                regNum: 2323,
                havNum: 2,
                startDate: '2000-08-11T18:44:48.000Z',
                status: 'Active',
                certifiedSalaryEntries: {
                  create: {
                    id: '8eef84f3-8911-429b-b6f7-f133052f88a6',
                    year: 2024,
                    amount: 463480,
                    partTimePercentage: 66,
                  },
                },
              },
            },
            certifiedIndexationStartOfYear: {
              create: {
                id: '48eb4fa9-950f-403e-a398-319cae5a5f39',
                accruedGrossAnnualOldAgePension: 2233.48,
                accruedGrossAnnualPartnersPension: 241.84,
                accruedGrossAnnualSinglesPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                grossAnnualDisabilityPension: 0,
              },
            },
            certifiedPensionCorrections: {
              create: {
                id: 'a9a3b3dc-ebd3-4537-8b60-ef6cd705a3ea',
                accruedGrossAnnualOldAgePension: 3,
                attainableGrossAnnualOldAgePension: 3,
                accruedGrossAnnualPartnersPension: 3,
                accruedGrossAnnualSinglesPension: 3,
                grossAnnualDisabilityPension: 3,
                extraAccruedGrossAnnualOldAgePension: 3,
                extraAccruedGrossAnnualPartnersPension: 3,
                correction: 3,
                year: '2020',
              },
            },
            certifiedPensionCorrectionsEndOfYear: {
              create: {
                id: '59608ed8-cf47-4bfb-9ee0-18e05e6ce9b7',
                accruedGrossAnnualOldAgePension: 11515.98,
                attainableGrossAnnualOldAgePension: 34268,
                accruedGrossAnnualPartnersPension: 8061.39,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 2,
                correction: 2,
                year: '2024',
              },
            },
            certifiedPensionCorrectionsStartOfYear: {
              create: {
                id: '57524f2f-e8f3-42cf-a8c3-e697d7a16a00',
                accruedGrossAnnualOldAgePension: 0,
                attainableGrossAnnualOldAgePension: 0,
                accruedGrossAnnualPartnersPension: 0,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                correction: 0,
                year: '2024',
              },
            },
            certifiedPensionInfo: {
              create: {
                id: '26c10720-e4f6-49c3-9676-6bffbff87be7',
                code: 10,
                codeDescription: 'asdfasd',
                accruedGrossAnnualOldAgePension: 12725.94,
                accruedGrossAnnualPartnersPension: 8908.36,
                accruedGrossAnnualSinglesPension: 0,
                attainableGrossAnnualOldAgePension: 34268,
                extraAccruedGrossAnnualOldAgePension: 44,
                extraAccruedGrossAnnualPartnersPension: 44,
                grossAnnualDisabilityPension: 0,
                pensionBase: 4444,
                accrualEndDate: '2024-12-31T08:12:49.000Z',
                accrualStartDate: '2024-01-01T08:10:44.000Z',
              },
            },
            certifiedPensionParameters: {
              create: {
                id: '1e11370b-bce1-4d41-a928-34150d949fe0',
                accrualPercentage: 13,
                annualMultiplier: 17616,
                offsetAmount: 3,
                partnersPensionPercentage: 65,
                retirementAge: 0,
                voluntaryContributionInterestRate: 25000,
                minimumPensionBase: 2024,
                year: '2024',
                effectiveDate: '2025-08-11T18:49:06.356Z',
              },
            },
            certifiedPensionsAccrual: {
              create: {
                id: '28e8132b-3574-4ec4-b113-5a1e5fa4c62a',
                pensionBase: 43224,
                opteAccrualAfterReferenceDate: 21542.36,
                opteAccrualToReferenceDate: 864.48,
                wpteAccrualToReferenceDate: 25.93,
              },
            },
            certifiedPersonalInfo: {
              create: {
                id: 'e82f1d5e-c109-4357-94a1-ac040757646c',
                firstName: 'asdf asd ',
                lastName: 'adsf asd',
              },
            },
          },
          {
            id: '25770c8c-bf14-4a77-9760-8f183367aebd',
            certificationYear: 2024,
            certificationStatus: 'pending',
            certifiedAt: '2025-08-11T18:39:20.000Z',
            certifiedById: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
            certifiedEmploymentInfo: {
              create: {
                id: '963aaf92-19f2-4175-8158-371254b073eb',
                employeeId: 'EMP33',
                department: 'Deep Well',
                position: 'Goody',
                regNum: 2323,
                havNum: 2,
                startDate: '2000-08-11T18:44:48.000Z',
                status: 'Active',
                certifiedSalaryEntries: {
                  create: {
                    id: 'ab50a0a0-7e12-4136-aee7-1e5c3139bd54',
                    year: 2024,
                    amount: 46880,
                    partTimePercentage: 67,
                  },
                },
              },
            },
            certifiedIndexationStartOfYear: {
              create: {
                id: 'bbb57ef6-4771-42c4-bacc-ce4159d86a5a',
                accruedGrossAnnualOldAgePension: 345.48,
                accruedGrossAnnualPartnersPension: 241.84,
                accruedGrossAnnualSinglesPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                grossAnnualDisabilityPension: 0,
              },
            },
            certifiedPensionCorrections: {
              create: {
                id: '16765c3b-4f42-42a0-91d7-ac30c23eab52',
                accruedGrossAnnualOldAgePension: 3,
                attainableGrossAnnualOldAgePension: 3,
                accruedGrossAnnualPartnersPension: 3,
                accruedGrossAnnualSinglesPension: 3,
                grossAnnualDisabilityPension: 3,
                extraAccruedGrossAnnualOldAgePension: 3,
                extraAccruedGrossAnnualPartnersPension: 3,
                correction: 3,
                year: '2020',
              },
            },
            certifiedPensionCorrectionsEndOfYear: {
              create: {
                id: 'f7c9a978-11f7-442d-9d6e-2fbce9a4237b',
                accruedGrossAnnualOldAgePension: 11515.98,
                attainableGrossAnnualOldAgePension: 34268,
                accruedGrossAnnualPartnersPension: 8061.39,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 2,
                correction: 2,
                year: '2024',
              },
            },
            certifiedPensionCorrectionsStartOfYear: {
              create: {
                id: '9f9ee88c-c75b-4a1b-82ec-ac9508c7d5ac',
                accruedGrossAnnualOldAgePension: 0,
                attainableGrossAnnualOldAgePension: 0,
                accruedGrossAnnualPartnersPension: 0,
                accruedGrossAnnualSinglesPension: 0,
                grossAnnualDisabilityPension: 0,
                extraAccruedGrossAnnualOldAgePension: 0,
                extraAccruedGrossAnnualPartnersPension: 0,
                correction: 0,
                year: '2024',
              },
            },
            certifiedPensionInfo: {
              create: {
                id: '8317ade3-fd1d-47de-bc43-9b24d1a59d27',
                code: 10,
                codeDescription: 'asdfasd',
                accruedGrossAnnualOldAgePension: 12725.94,
                accruedGrossAnnualPartnersPension: 8908.36,
                accruedGrossAnnualSinglesPension: 0,
                attainableGrossAnnualOldAgePension: 34268,
                extraAccruedGrossAnnualOldAgePension: 44,
                extraAccruedGrossAnnualPartnersPension: 44,
                grossAnnualDisabilityPension: 0,
                pensionBase: 4444,
                accrualEndDate: '2024-12-31T08:12:49.000Z',
                accrualStartDate: '2024-01-01T08:10:44.000Z',
              },
            },
            certifiedPensionParameters: {
              create: {
                id: 'db983c79-b7e5-4ff7-9667-4592107cc7df',
                accrualPercentage: 13,
                annualMultiplier: 17616,
                offsetAmount: 3,
                partnersPensionPercentage: 65,
                retirementAge: 0,
                voluntaryContributionInterestRate: 25000,
                minimumPensionBase: 2024,
                year: '2024',
                effectiveDate: '2025-08-11T18:49:06.356Z',
              },
            },
            certifiedPensionsAccrual: {
              create: {
                id: '093d7472-589f-4147-885e-fab674d120f6',
                pensionBase: 43224,
                opteAccrualAfterReferenceDate: 21542.36,
                opteAccrualToReferenceDate: 864.48,
                wpteAccrualToReferenceDate: 25.93,
              },
            },
            certifiedPersonalInfo: {
              create: {
                id: '8bf8fbe3-ae95-4125-b941-8659310a059f',
                firstName: 'Johniola',
                lastName: 'adsf asd',
              },
            },
          },
        ],
      },
    },
  });

  console.log('Seeding finished.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
