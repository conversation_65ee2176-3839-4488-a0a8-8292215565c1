import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

async function main() {
    console.log('Start seeding...')

    // Helper function to generate varied but realistic data
    function generateVariation(
        baseValue: number,
        variationPercent: number = 10
    ): number {
        const variation = (baseValue * variationPercent) / 100
        return (
            baseValue + faker.number.float({ min: -variation, max: variation })
        )
    }

    const basePension = faker.number.float({ min: 15000, max: 75000 })
    const basePartnersPension = basePension * 0.7
    const baseSinglesPension = basePension * 0.5

    // Roles
    await prisma.role.upsert({
        where: { id: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f' },
        update: {},
        create: {
            id: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
            name: '<PERSON>min',
            description: 'System Admin',
        },
    })
    await prisma.role.upsert({
        where: { id: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e' },
        update: {},
        create: {
            id: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e',
            name: 'Reviewer',
            description: 'Reviewer',
        },
    })
    await prisma.role.upsert({
        where: { id: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8' },
        update: {},
        create: {
            id: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8',
            name: 'Accountant',
            description: 'Accountant',
        },
    })
    await prisma.role.upsert({
        where: { id: '7da8cfe0-de11-414b-89f0-a03c5ce366de' },
        update: {},
        create: {
            id: '7da8cfe0-de11-414b-89f0-a03c5ce366de',
            name: 'Editor',
            description: 'Editor',
        },
    })

    // Users
    await prisma.user.upsert({
        where: { id: 'abcedefa-25bf-4f85-8bd3-dea0397a489d' },
        update: {},
        create: {
            id: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            firebaseUid: 'CSpw5IKuoERVDyD9jSIF6XOfedX2',
            email: '<EMAIL>',
            firstname: 'test',
            lastname: 'editor',
            roleId: '7da8cfe0-de11-414b-89f0-a03c5ce366de',
        },
    })
    await prisma.user.upsert({
        where: { id: 'be196c0f-ee7b-42a3-9163-885f649e65ef' },
        update: {},
        create: {
            id: 'be196c0f-ee7b-42a3-9163-885f649e65ef',
            firebaseUid: 'bwzgkNU2SSSCA1HzoKJVJdI7mqL2',
            email: '<EMAIL>',
            firstname: 'Admin',
            lastname: 'Admin',
            roleId: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
        },
    })
    await prisma.user.upsert({
        where: { id: '24c4a88c-123c-42d3-b24e-693c51aca1ed' },
        update: {},
        create: {
            id: '24c4a88c-123c-42d3-b24e-693c51aca1ed',
            firebaseUid: 'MOZfw8bnz2UEPnKUPfRE3Od1BJt2',
            email: '<EMAIL>',
            firstname: 'Super',
            lastname: 'Admin',
            roleId: '8e56460b-f1ff-4c4d-b9da-a86858b8fe3f',
        },
    })
    await prisma.user.upsert({
        where: { id: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38' },
        update: {},
        create: {
            id: 'd87f9d08-755c-413d-ac0a-9e0411b5ea38',
            firebaseUid: '1EPxoLbe3maU6x4DC40J0icDAkI2',
            email: '<EMAIL>',
            firstname: 'Accountant',
            lastname: 'Accountant',
            roleId: '61e0bf89-2c41-401b-a65c-8ae8d36ffde8',
        },
    })
    await prisma.user.upsert({
        where: { id: '82920d1b-c3b1-4299-9581-086cddf21615' },
        update: {},
        create: {
            id: '82920d1b-c3b1-4299-9581-086cddf21615',
            firebaseUid: 'e5BM87KF00OEwlKFozMD1KMSCAY2',
            email: '<EMAIL>',
            firstname: 'Reviewer',
            lastname: 'Reviewer',
            roleId: '40fd7a3a-e06b-47c6-bf38-0f33d3bd6c5e',
        },
    })

    // PensionParameters
    await prisma.pensionParameters.upsert({
        where: { id: '6d7bf7ad-1594-48ca-918b-f49d06cda067' },
        update: {},
        create: {
            id: '6d7bf7ad-1594-48ca-918b-f49d06cda067',
            accrualPercentage: 2,
            annualMultiplier: 13,
            offsetAmount: 17616,
            partnersPensionPercentage: 70,
            retirementAge: 65,
            voluntaryContributionInterestRate: 0.04,
            minimumPensionBase: 25000,
            year: '2025',
            effectiveDate: '2025-08-14T00:00:00.000Z',
            userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            status: 'active',
            indexationPercentage: 3,
        },
    })
    await prisma.pensionParameters.upsert({
        where: { id: 'ffadbb8c-19bb-4cb0-a61d-d30cbd3ab4b1' },
        update: {},
        create: {
            id: 'ffadbb8c-19bb-4cb0-a61d-d30cbd3ab4b1',
            accrualPercentage: 2,
            annualMultiplier: 13,
            offsetAmount: 17616,
            partnersPensionPercentage: 70,
            retirementAge: 65,
            voluntaryContributionInterestRate: 0.04,
            minimumPensionBase: 25000,
            year: '2022',
            effectiveDate: '2025-08-14T00:00:00.000Z',
            userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            status: 'active',
            indexationPercentage: 3,
        },
    })
    await prisma.pensionParameters.upsert({
        where: { id: '0fe4c317-7308-4fd1-b003-ac1496b53148' },
        update: {},
        create: {
            id: '0fe4c317-7308-4fd1-b003-ac1496b53148',
            accrualPercentage: 2,
            annualMultiplier: 13,
            offsetAmount: 17616,
            partnersPensionPercentage: 70,
            retirementAge: 65,
            voluntaryContributionInterestRate: 0.04,
            minimumPensionBase: 25000,
            year: '2020',
            effectiveDate: '2025-08-14T00:00:00.000Z',
            userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            status: 'active',
            indexationPercentage: 3,
        },
    })
    await prisma.pensionParameters.upsert({
        where: { id: 'f1d6e5a3-8f82-438b-9c8f-efbc9f06b19d' },
        update: {},
        create: {
            id: 'f1d6e5a3-8f82-438b-9c8f-efbc9f06b19d',
            accrualPercentage: 2,
            annualMultiplier: 13,
            offsetAmount: 17616,
            partnersPensionPercentage: 70,
            retirementAge: 65,
            voluntaryContributionInterestRate: 0.04,
            minimumPensionBase: 25000,
            year: '2023',
            effectiveDate: '2025-08-14T00:00:00.000Z',
            userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            status: 'active',
            indexationPercentage: 3,
        },
    })
    await prisma.pensionParameters.upsert({
        where: { id: 'f2654f95-4a68-41fe-bab8-4059a7b8e2a2' },
        update: {},
        create: {
            id: 'f2654f95-4a68-41fe-bab8-4059a7b8e2a2',
            accrualPercentage: 2,
            annualMultiplier: 13,
            offsetAmount: 17616,
            partnersPensionPercentage: 70,
            retirementAge: 65,
            voluntaryContributionInterestRate: 0.04,
            minimumPensionBase: 25000,
            year: '2024',
            effectiveDate: '2025-08-14T00:00:00.000Z',
            userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            status: 'active',
            indexationPercentage: 3,
        },
    })
    await prisma.pensionParameters.upsert({
        where: { id: '486cf38a-6e3d-45c4-ba51-1b0de456b297' },
        update: {},
        create: {
            id: '486cf38a-6e3d-45c4-ba51-1b0de456b297',
            accrualPercentage: 2,
            annualMultiplier: 13,
            offsetAmount: 17616,
            partnersPensionPercentage: 70,
            retirementAge: 65,
            voluntaryContributionInterestRate: 0.04,
            minimumPensionBase: 25000,
            year: '2021',
            effectiveDate: '2025-08-14T00:00:00.000Z',
            userId: 'abcedefa-25bf-4f85-8bd3-dea0397a489d',
            status: 'active',
            indexationPercentage: 3,
        },
    })

    // 1. Create a user to associate with the certified data
    const user = await prisma.user.create({
        data: {
            firebaseUid: faker.string.uuid(),
            email: faker.internet.email(),
            firstname: faker.person.firstName(),
            lastname: faker.person.lastName(),
            role: {
                connectOrCreate: {
                    where: { name: 'admin' },
                    create: { name: 'admin', description: 'Administrator' },
                },
            },
        },
    })

    // 2. Create 1000 participants
    for (let i = 0; i < 1000; i++) {
        const participant = await prisma.participant.create({
            data: {
                createdBy: user.id,
                updatedBy: user.id,
                personalInfo: {
                    create: {
                        firstName: faker.person.firstName(),
                        lastName: faker.person.lastName(),
                        sex: faker.person.sex(),
                        email: faker.internet.email(),
                        phone: faker.phone.number(),
                        maritalStatus: 'Single',
                        birthDay: faker.number.int({ min: 1, max: 28 }),
                        birthMonth: faker.number.int({ min: 1, max: 12 }),
                        birthYear: faker.number.int({ min: 1950, max: 2000 }),
                    },
                },
            },
        })

        // 3. For each participant, create certified data for 2023, 2024, and 2025
        for (const year of [2023, 2024, 2025]) {
            await prisma.certifiedData.create({
                data: {
                    participantId: participant.id,
                    certificationYear: year,
                    certificationStatus:
                        year === 2023 ? 'completed' : 'pending',
                    certifiedAt: new Date(),
                    certifiedById: user.id,
                    certifiedPensionInfo: {
                        create: {
                            code: faker.number.int({ max: 2147483647 }),
                            codeDescription: faker.lorem.sentence(),
                            accrualStartDate: faker.date.past(),
                            accrualEndDate: faker.date.future(),
                            accruedGrossAnnualOldAgePension: faker.number.float(
                                { min: 10000, max: 50000 }
                            ),
                            accruedGrossAnnualPartnersPension:
                                faker.number.float({ min: 5000, max: 25000 }),
                        },
                    },
                    certifiedEmploymentInfo: {
                        create: {
                            employeeId: faker.string.uuid(),
                            department: faker.commerce.department(),
                            position: faker.person.jobTitle(),
                            startDate: faker.date.past(),
                            status: 'active',
                            certifiedSalaryEntries: {
                                create: {
                                    year: year,
                                    amount: faker.number.float({
                                        min: 30000,
                                        max: 100000,
                                    }),
                                    partTimePercentage: faker.number.float({
                                        min: 0.5,
                                        max: 1,
                                    }),
                                },
                            },
                        },
                    },
                    certifiedPersonalInfo: {
                        create: {
                            firstName: faker.person.firstName(),
                            lastName: faker.person.lastName(),
                            sex: faker.person.sex(),
                            email: faker.internet.email(),
                            phone: faker.phone.number(),
                            maritalStatus: 'Single',
                            birthDay: faker.number.int({ min: 1, max: 28 }),
                            birthMonth: faker.number.int({ min: 1, max: 12 }),
                            birthYear: faker.number.int({
                                min: 1950,
                                max: 2000,
                            }),
                        },
                    },
                    certifiedAddress: {
                        create: {
                            street: faker.location.street(),
                            houseNumber: faker.number
                                .int({ min: 1, max: 200 })
                                .toString(),
                            postalCode: faker.location.zipCode(),
                            city: faker.location.city(),
                            country: faker.location.country(),
                        },
                    },
                    certifiedIndexationStartOfYear: {
                        create: {
                            accruedGrossAnnualOldAgePension: faker.number.float(
                                { min: 10000, max: 50000 }
                            ),
                            accruedGrossAnnualPartnersPension:
                                faker.number.float({ min: 5000, max: 25000 }),
                        },
                    },
                    certifiedPensionCorrections: {
                        create: {
                            correction: faker.number.float({
                                min: -5000,
                                max: 5000,
                            }),
                            year: year.toString(),
                        },
                    },
                    certifiedVoluntaryContributions: {
                        create: {
                            contributions: JSON.stringify([
                                {
                                    year: year,
                                    amount: faker.number.float({
                                        min: 100,
                                        max: 1000,
                                    }),
                                },
                            ]),
                        },
                    },
                    certifiedPensionParameters: {
                        create: {
                            accrualPercentage: faker.number.float({
                                min: 1,
                                max: 3,
                            }),
                            annualMultiplier: faker.number.float({
                                min: 12,
                                max: 14,
                            }),
                            offsetAmount: faker.number.float({
                                min: 15000,
                                max: 20000,
                            }),
                            partnersPensionPercentage: faker.number.float({
                                min: 60,
                                max: 80,
                            }),
                            retirementAge: faker.number.int({
                                min: 60,
                                max: 70,
                            }),
                            voluntaryContributionInterestRate:
                                faker.number.float({ min: 1, max: 5 }),
                            minimumPensionBase: faker.number.float({
                                min: 0,
                                max: 1000,
                            }),
                            indexationPercentage: faker.number.float({
                                min: 1,
                                max: 3,
                            }),
                            year: year.toString(),
                            effectiveDate: faker.date.past(),
                        },
                    },
                    // NEW: CertifiedPensionCorrectionsStartOfYear
                    certifiedPensionCorrectionsStartOfYear: {
                        create: {
                            accruedGrossAnnualOldAgePension: generateVariation(
                                basePension,
                                2
                            ),
                            attainableGrossAnnualOldAgePension:
                                generateVariation(basePension * 1.5, 2),
                            accruedGrossAnnualPartnersPension:
                                generateVariation(basePartnersPension, 2),
                            accruedGrossAnnualSinglesPension: generateVariation(
                                baseSinglesPension,
                                2
                            ),
                            grossAnnualDisabilityPension: generateVariation(
                                basePension * 0.75,
                                2
                            ),
                            extraAccruedGrossAnnualOldAgePension:
                                faker.number.float({ min: 0, max: 2000 }),
                            extraAccruedGrossAnnualPartnersPension:
                                faker.number.float({ min: 0, max: 1000 }),
                            correction: faker.number.float({
                                min: -3000,
                                max: 3000,
                            }),
                            year: year.toString(),
                        },
                    },

                    // NEW: CertifiedPensionCorrectionsEndOfYear
                    certifiedPensionCorrectionsEndOfYear: {
                        create: {
                            accruedGrossAnnualOldAgePension: generateVariation(
                                basePension,
                                8
                            ),
                            attainableGrossAnnualOldAgePension:
                                generateVariation(basePension * 1.5, 8),
                            accruedGrossAnnualPartnersPension:
                                generateVariation(basePartnersPension, 8),
                            accruedGrossAnnualSinglesPension: generateVariation(
                                baseSinglesPension,
                                8
                            ),
                            grossAnnualDisabilityPension: generateVariation(
                                basePension * 0.75,
                                8
                            ),
                            extraAccruedGrossAnnualOldAgePension:
                                faker.number.float({ min: 0, max: 4000 }),
                            extraAccruedGrossAnnualPartnersPension:
                                faker.number.float({ min: 0, max: 2000 }),
                            correction: faker.number.float({
                                min: -4000,
                                max: 4000,
                            }),
                            year: year.toString(),
                        },
                    },

                    // NEW: CertifiedPensionsAccrual
                    certifiedPensionsAccrual: {
                        create: {
                            pensionBase: faker.number.float({
                                min: 30000,
                                max: 100000,
                            }),
                            opteAccrualToReferenceDate: faker.number.float({
                                min: 5000,
                                max: 25000,
                            }),
                            wpteAccrualToReferenceDate: faker.number.float({
                                min: 3000,
                                max: 15000,
                            }),
                            opteAccrualAfterReferenceDate: faker.number.float({
                                min: 1000,
                                max: 10000,
                            }),
                        },
                    },
                },
            })
        }
    }

    console.log('Seeding finished.')
}

main()
    .catch((e) => {
        console.error(e)
        process.exit(1)
    })
    .finally(async () => {
        await prisma.$disconnect()
    })
