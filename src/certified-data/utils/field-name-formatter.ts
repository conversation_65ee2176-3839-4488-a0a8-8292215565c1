/**
 * Utility to format field names for display
 */

const FIELD_NAME_OVERRIDES: Record<string, string> = {
    // Personal Info
    'certifiedPersonalInfo.firstName': 'First Name',
    'certifiedPersonalInfo.lastName': 'Last Name',
    'certifiedPersonalInfo.email': 'Email Address',
    'certifiedPersonalInfo.phone': 'Phone Number',
    'certifiedPersonalInfo.birthDay': 'Birth Day',
    'certifiedPersonalInfo.birthMonth': 'Birth Month',
    'certifiedPersonalInfo.birthYear': 'Birth Year',
    'certifiedPersonalInfo.sex': 'Gender',
    'certifiedPersonalInfo.maritalStatus': 'Marital Status',

    // Employment Info
    'certifiedEmploymentInfo.department': 'Department',
    'certifiedEmploymentInfo.position': 'Position',
    'certifiedEmploymentInfo.startDate': 'Employment Start Date',
    'certifiedEmploymentInfo.endDate': 'Employment End Date',
    'certifiedEmploymentInfo.employmentStatus': 'Employment Status',
    'certifiedEmploymentInfo.contractType': 'Contract Type',

    // Pension Info
    'certifiedPensionInfo.code': 'Pension Code',
    'certifiedPensionInfo.codeDescription': 'Pension Code Description',
    'certifiedPensionInfo.previousCode': 'Previous Pension Code',
    'certifiedPensionInfo.previousCodeEffectiveDate':
        'Previous Code Effective Date',
    'certifiedPensionInfo.startDate': 'Pension Start Date',

    // Address
    'certifiedAddress.street': 'Street Address',
    'certifiedAddress.houseNumber': 'House Number',
    'certifiedAddress.postalCode': 'Postal Code',
    'certifiedAddress.city': 'City',
    'certifiedAddress.state': 'State/Province',
    'certifiedAddress.country': 'Country',

    // Indexation Start of Year
    'certifiedIndexationStartOfYear.accruedGrossAnnualOldAgePension':
        'Accrued Gross Annual Old Age Pension',
    'certifiedIndexationStartOfYear.accruedGrossAnnualPartnersPension':
        'Accrued Gross Annual Partners Pension',
    'certifiedIndexationStartOfYear.accruedGrossAnnualSinglesPension':
        'Accrued Gross Annual Singles Pension',
    'certifiedIndexationStartOfYear.attainableGrossAnnualOldAgePension':
        'Attainable Gross Annual Old Age Pension',
    'certifiedIndexationStartOfYear.attainableGrossAnnualPartnersPension':
        'Attainable Gross Annual Partners Pension',
    'certifiedIndexationStartOfYear.attainableGrossAnnualSinglesPension':
        'Attainable Gross Annual Singles Pension',
    'certifiedIndexationStartOfYear.extraAccruedGrossAnnualOldAgePension':
        'Extra Accrued Gross Annual Old Age Pension',
    'certifiedIndexationStartOfYear.extraAccruedGrossAnnualPartnersPension':
        'Extra Accrued Gross Annual Partners Pension',
    'certifiedIndexationStartOfYear.grossAnnualDisabilityPension':
        'Gross Annual Disability Pension',

    // Pension Corrections
    'certifiedPensionCorrections.accruedGrossAnnualOldAgePension':
        'Accrued Gross Annual Old Age Pension',
    'certifiedPensionCorrections.accruedGrossAnnualPartnersPension':
        'Accrued Gross Annual Partners Pension',
    'certifiedPensionCorrections.accruedGrossAnnualSinglesPension':
        'Accrued Gross Annual Singles Pension',
    'certifiedPensionCorrections.attainableGrossAnnualOldAgePension':
        'Attainable Gross Annual Old Age Pension',
    'certifiedPensionCorrections.attainableGrossAnnualPartnersPension':
        'Attainable Gross Annual Partners Pension',
    'certifiedPensionCorrections.attainableGrossAnnualSinglesPension':
        'Attainable Gross Annual Singles Pension',
    'certifiedPensionCorrections.extraAccruedGrossAnnualOldAgePension':
        'Extra Accrued Gross Annual Old Age Pension',
    'certifiedPensionCorrections.extraAccruedGrossAnnualPartnersPension':
        'Extra Accrued Gross Annual Partners Pension',
    'certifiedPensionCorrections.grossAnnualDisabilityPension':
        'Gross Annual Disability Pension',

    // Pension Corrections Start/End of Year
    'certifiedPensionCorrectionsStartOfYear.accruedGrossAnnualOldAgePension':
        'Accrued Gross Annual Old Age Pension (Start of Year)',
    'certifiedPensionCorrectionsStartOfYear.accruedGrossAnnualPartnersPension':
        'Accrued Gross Annual Partners Pension (Start of Year)',
    'certifiedPensionCorrectionsStartOfYear.accruedGrossAnnualSinglesPension':
        'Accrued Gross Annual Singles Pension (Start of Year)',
    'certifiedPensionCorrectionsEndOfYear.accruedGrossAnnualOldAgePension':
        'Accrued Gross Annual Old Age Pension (End of Year)',
    'certifiedPensionCorrectionsEndOfYear.accruedGrossAnnualPartnersPension':
        'Accrued Gross Annual Partners Pension (End of Year)',
    'certifiedPensionCorrectionsEndOfYear.accruedGrossAnnualSinglesPension':
        'Accrued Gross Annual Singles Pension (End of Year)',

    // Voluntary Contributions
    'certifiedVoluntaryContributions.totalContributions':
        'Total Voluntary Contributions',
    'certifiedVoluntaryContributions.interestRate': 'Interest Rate',
    'certifiedVoluntaryContributions.accruedInterest': 'Accrued Interest',

    // Pension Parameters
    'certifiedPensionParameters.retirementAge': 'Retirement Age',
    'certifiedPensionParameters.indexationPercentage': 'Indexation Percentage',
    'certifiedPensionParameters.partnersPensionPercentage':
        'Partners Pension Percentage',
    'certifiedPensionParameters.offsetAmount': 'Offset Amount',
    'certifiedPensionParameters.minimumPensionBase': 'Minimum Pension Base',
    'certifiedPensionParameters.year': 'Parameter Year',

    // Salary Entries
    'certifiedSalaryEntries.amount': 'Salary Amount',
    'certifiedSalaryEntries.partTimePercentage': 'Part-Time Percentage',
    'certifiedSalaryEntries.year': 'Salary Year',
}

/**
 * Convert camelCase or PascalCase to Title Case
 */
function camelToTitleCase(str: string): string {
    // Add space before capital letters
    const withSpaces = str.replace(/([A-Z])/g, ' $1')
    // Capitalize first letter and trim
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1).trim()
}

/**
 * Format a field path to a human-readable display name
 */
export function formatFieldName(fieldPath: string): string {
    // Check for override first
    if (FIELD_NAME_OVERRIDES[fieldPath]) {
        return FIELD_NAME_OVERRIDES[fieldPath]
    }

    // Handle array notation for salary entries
    const salaryEntryMatch = fieldPath.match(
        /^certifiedSalaryEntries\[(\d+)\]\.(.+)$/
    )
    if (salaryEntryMatch) {
        const year = salaryEntryMatch[1]
        const field = salaryEntryMatch[2]
        const baseKey = `certifiedSalaryEntries.${field}`
        const baseName =
            FIELD_NAME_OVERRIDES[baseKey] || camelToTitleCase(field)
        return `${baseName} (${year})`
    }

    // Split the path into parts
    const parts = fieldPath.split('.')

    // Handle single field (no dot notation)
    if (parts.length === 1) {
        return camelToTitleCase(parts[0])
    }

    // Format entity name
    const entity = parts[0]
        .replace('certified', '')
        .replace(/([A-Z])/g, ' $1')
        .trim()

    // Format field name
    const field = parts.slice(1).join(' ')
    const formattedField = camelToTitleCase(field)

    // Combine entity and field
    return `${formattedField}`
}

/**
 * Get a short display name for the entity type
 */
export function formatEntityName(entityType: string): string {
    const entityNameMap: Record<string, string> = {
        certifiedPersonalInfo: 'Personal Info',
        certifiedEmploymentInfo: 'Employment Info',
        certifiedPensionInfo: 'Pension Info',
        certifiedAddress: 'Address',
        certifiedIndexationStartOfYear: 'Indexation',
        certifiedPensionCorrections: 'Pension Corrections',
        certifiedPensionCorrectionsStartOfYear: 'Corrections (Start)',
        certifiedPensionCorrectionsEndOfYear: 'Corrections (End)',
        certifiedVoluntaryContributions: 'Voluntary Contributions',
        certifiedPensionParameters: 'Pension Parameters',
        certifiedSalaryEntries: 'Salary Entries',
        certifiedChildInfo: 'Child Info',
        certifiedPartnerInfo: 'Partner Info',
    }

    return entityNameMap[entityType] || camelToTitleCase(entityType)
}
