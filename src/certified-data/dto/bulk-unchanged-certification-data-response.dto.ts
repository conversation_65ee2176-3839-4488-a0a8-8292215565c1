import { ObjectType, Field, Int } from '@nestjs/graphql'
import { IsArray, IsNumber, IsString, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CertifiedDataWithChanges } from '../entities/certified-data-with-changes.entity'
import { GraphQLJSONObject } from 'graphql-type-json'

@ObjectType()
export class BulkUnchangedStats {
    @Field(() => Int)
    @IsNumber()
    totalParticipants: number

    @Field(() => Int)
    @IsNumber()
    totalUnchanged: number

    @Field(() => Int)
    @IsNumber()
    totalFields: number

    @Field(() => Int)
    @IsNumber()
    percentageUnchanged: number

    @Field(() => Int)
    @IsNumber()
    year: number
}

@ObjectType()
export class BulkUnchangedPattern {
    @Field()
    @IsString()
    field: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    displayName?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    fieldId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    certTable?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    fieldName?: string

    @Field(() => Int)
    @IsNumber()
    count: number

    @Field(() => [GraphQLJSONObject])
    @IsArray()
    participants: any[]
}

@ObjectType()
export class BulkUnchangedCertificationDataResponse {
    @Field(() => [CertifiedDataWithChanges])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CertifiedDataWithChanges)
    certifications: CertifiedDataWithChanges[]

    @Field(() => BulkUnchangedStats)
    @ValidateNested()
    @Type(() => BulkUnchangedStats)
    stats: BulkUnchangedStats

    @Field(() => [BulkUnchangedPattern])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => BulkUnchangedPattern)
    unchangedPatterns: BulkUnchangedPattern[]
}
