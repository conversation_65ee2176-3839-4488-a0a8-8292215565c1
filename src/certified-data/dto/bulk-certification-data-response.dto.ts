import { ObjectType, Field, Int } from '@nestjs/graphql'
import { IsArray, IsNumber, IsString, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CertifiedDataWithChanges } from '../entities/certified-data-with-changes.entity'
import { GraphQLJSONObject } from 'graphql-type-json'

@ObjectType()
export class BulkCertificationStats {
    @Field(() => Int)
    @IsNumber()
    totalParticipants: number

    @Field(() => Int)
    @IsNumber()
    totalChanges: number

    @Field(() => Int)
    @IsNumber()
    noChanges: number

    @Field(() => Int)
    @IsNumber()
    highRisk: number

    @Field(() => Int)
    @IsNumber()
    year: number
}

@ObjectType()
export class BulkChangePattern {
    @Field()
    @IsString()
    field: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    displayName?: string

    @Field(() => Int)
    @IsNumber()
    count: number

    @Field(() => [GraphQLJSONObject])
    @IsArray()
    participants: any[]
}

@ObjectType()
export class BulkCertificationDataResponse {
    @Field(() => [CertifiedDataWithChanges])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CertifiedDataWithChanges)
    certifications: CertifiedDataWithChanges[]

    @Field(() => BulkCertificationStats)
    @ValidateNested()
    @Type(() => BulkCertificationStats)
    stats: BulkCertificationStats

    @Field(() => [BulkChangePattern])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => BulkChangePattern)
    changePatterns: BulkChangePattern[]
}
