import { toPrismaDate } from './date.utils'

/**
 * Field type mappings for all certified models
 */
const FIELD_TYPE_MAPPINGS = {
    // Date fields across all models
    dateFields: [
        'dateOfBirth',
        'startDate',
        'endDate',
        'effectiveDate',
        'retirementDate',
        'certifiedAt',
        'createdAt',
        'updatedAt',
        'reviewedAt',
        'accrualStartDate',
        'accrualEndDate',
        'codeEffectiveDate',
        'previousCodeEffectiveDate',
    ],

    // Integer fields
    intFields: [
        'code',
        'previousCode',
        'year',
        'birthDay',
        'birthMonth',
        'birthYear',
        'regNum',
        'havNum',
        'retirementAge',
        'certificationYear',
    ],

    // Float fields
    floatFields: [
        'amount',
        'partTimePercentage',
        'accruedGrossAnnualOldAgePension',
        'accruedGrossAnnualPartnersPension',
        'accruedGrossAnnualSinglesPension',
        'attainableGrossAnnualOldAgePension',
        'extraAccruedGrossAnnualOldAgePension',
        'extraAccruedGrossAnnualPartnersPension',
        'grossAnnualDisabilityPension',
        'pensionBase',
        'accrualPercentage',
        'annualMultiplier',
        'offsetAmount',
        'partnersPensionPercentage',
        'voluntaryContributionInterestRate',
        'minimumPensionBase',
        'indexationPercentage',
        'correction',
        'opteAccrualToReferenceDate',
        'wpteAccrualToReferenceDate',
        'opteAccrualAfterReferenceDate',
    ],

    // Boolean fields
    booleanFields: [
        'isDeceased',
        'isCurrent',
        'isOrphan',
        'isStudying',
        'submittedForReview',
    ],
}

/**
 * Process a value for a change update based on the field type
 * @param path The field name/path
 * @param value The raw value to process
 * @returns The processed value with correct type
 */
export function processChangeValue(path: string, value: any): any {
    // Handle null/undefined
    if (value === null || value === undefined || value === '') {
        return null
    }

    // Check for date fields
    if (FIELD_TYPE_MAPPINGS.dateFields.includes(path)) {
        return toPrismaDate(value)
    }

    // Check for integer fields
    if (FIELD_TYPE_MAPPINGS.intFields.includes(path)) {
        const numValue = Number(value)
        if (isNaN(numValue)) {
            throw new Error(`Invalid integer value for field ${path}: ${value}`)
        }
        return Math.floor(numValue) // Ensure it's an integer
    }

    // Check for float fields
    if (FIELD_TYPE_MAPPINGS.floatFields.includes(path)) {
        const numValue = Number(value)
        if (isNaN(numValue)) {
            throw new Error(`Invalid numeric value for field ${path}: ${value}`)
        }
        return numValue
    }

    // Check for boolean fields
    if (FIELD_TYPE_MAPPINGS.booleanFields.includes(path)) {
        if (typeof value === 'boolean') {
            return value
        }
        if (typeof value === 'string') {
            const lowerValue = value.toLowerCase()
            if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes') {
                return true
            }
            if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no') {
                return false
            }
        }
        if (typeof value === 'number') {
            return value !== 0
        }
        throw new Error(`Invalid boolean value for field ${path}: ${value}`)
    }

    // Return as-is for string and other types
    return value
}

/**
 * Prepare update data for a Prisma update operation
 * @param path The field name/path
 * @param value The raw value to process
 * @returns An object ready for Prisma update
 */
export function prepareUpdateData(path: string, value: any): Record<string, any> {
    const processedValue = processChangeValue(path, value)
    return {
        [path]: processedValue,
    }
}
