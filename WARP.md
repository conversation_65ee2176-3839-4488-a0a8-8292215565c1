# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

Pension Admin API is a NestJS-based GraphQL API for pension administration. It manages participant data, pension calculations, employment information, and certified data snapshots for pension-related records.

## Common Development Commands

### Running the Application

```bash
# Development mode with hot reload
npm run start:dev

# Production mode
npm run start:prod

# Debug mode
npm run start:debug

# Using Docker
npm run docker              # Start all services with docker-compose
npm run docker:stop         # Stop all Docker services
```

### Database Management

```bash
# Prisma migrations
npm run migrate:dev                    # Run migrations in development
npm run migrate:deploy                 # Deploy migrations to production
npm run migrate:dev:create             # Create new migration without applying
npm run migrate:reset                  # Reset database (CAUTION: deletes all data)
npm run prisma:studio                  # Open Prisma Studio GUI

# Seeding
npm run db:seed                        # Run default seeder
npm run db:seed:certified-data        # Seed certified data
npm run db:seed:demo-data             # Seed demo data
```

### Testing

```bash
# Unit tests
npm test                    # Run all tests
npm run test:watch         # Run tests in watch mode
npm run test:cov           # Run tests with coverage
npm run test:debug         # Debug tests

# E2E tests
npm run test:e2e

# Test certified models
npm run test:certified-models
```

### Code Quality

```bash
npm run lint               # Run ESLint and auto-fix issues
npm run format             # Format code with Prettier
```

### Stripe Webhook Development

```bash
npm run stripe:listen-webhook    # Forward Stripe webhooks to localhost:8900/stripe/webhook
```

## Architecture Overview

### Stack
- **Framework**: NestJS with GraphQL (Apollo Server)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Firebase Admin SDK
- **Container**: Docker with multi-stage builds
- **Real-time**: GraphQL Subscriptions (WebSocket)
- **Email**: Nodemailer with Handlebars templates
- **Payment**: Stripe integration

### Module Structure

The API is organized into domain-specific modules in `src/`:

**Core Modules:**
- `app.module.ts` - Main application module with GraphQL and middleware configuration
- `prisma.module.ts` & `prisma.service.ts` - Database connection management
- `auth/` - Firebase authentication integration

**Domain Modules:**
- `participant/` - Core participant management
- `personal-info/`, `employment-info/`, `pension-info/` - Participant data domains
- `address/`, `child/`, `partner-info/` - Related entities

**Certified Data System:**
- `certified-data/` - Base certified data management
- `certified-*-info/` modules - Certified snapshots of various data types
- Each certified module stores yearly snapshots with approval workflows

**Change Management:**
- `change-proposal/` - Change request workflow system
- `change-data/` - Track individual field changes
- `certification-reject-reason/` - Handle certification rejection reasons

**Supporting Modules:**
- `pension-calculations/` - Centralized pension calculation logic (previously frontend)
- `pension-parameters/` - Configurable pension calculation parameters
- `pension-corrections/` - Pension correction management
- `audit-log/` - System-wide audit logging
- `notifications/` - Push notification system
- `email-handler/` - Email service integration

### Key Design Patterns

1. **GraphQL Resolvers**: Each module exposes GraphQL queries/mutations via resolver classes
2. **Service Layer**: Business logic is encapsulated in service classes
3. **Prisma Models**: Database schema defined in `prisma/schema.prisma`
4. **Module Isolation**: Each feature is self-contained in its own module
5. **Dependency Injection**: NestJS DI container manages service dependencies

### Database Schema

The database uses PostgreSQL with these core relationships:
- `Participant` is the central entity
- `PersonalInfo`, `EmploymentInfo`, `PensionInfo` have 1:1 relationships with Participant
- `CertifiedData` stores yearly snapshots linked to Participants
- `ChangeProposal` manages proposed changes with approval workflow
- `User` represents system users with role-based access

### Environment Configuration

Required environment variables (see `.env.development` or `.env.production`):
```
DATABASE_URL=postgresql://user:password@localhost:5432/pensionadmin
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_PRIVATE_KEY=your-private-key
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=your-email
MAIL_PASS=your-password
```

### Docker Development

The project uses Docker Compose with services:
- `postgres` - PostgreSQL database (port 5432)
- `postgis` - PostGIS-enabled PostgreSQL (port 5433)
- `app` - NestJS application (port 3000)
- `pgadmin` - Database management UI (port 5050)

Default credentials for development:
- PostgreSQL: `aximilli1212/aximilli1212`
- pgAdmin: `<EMAIL>/pgadmin4`

### GraphQL API

- Playground available at `http://localhost:3500/graphql` in development
- Schema auto-generated at `src/schema.gql`
- Supports queries, mutations, and subscriptions
- WebSocket subscriptions use Firebase token authentication

### Pension Calculations

The `pension-calculations` module provides centralized calculation logic:
- Old-Age Pension (OP-TE) calculations
- Widows/Partners Pension (WP-TE) calculations
- Retirement date calculations
- Accrual period calculations
- All calculations use 30/360 day count convention

See `src/pension-calculations/README.md` for detailed API documentation.

### Testing Strategy

- Unit tests alongside source files (`*.spec.ts`)
- E2E tests in `test/` directory
- Jest configuration in `package.json`
- Coverage reports in `coverage/` directory

### Deployment

Production deployment uses:
- Multi-stage Docker build for optimization
- Prisma migrations deployed separately
- Environment-specific configuration files
- GCP Cloud Run deployment available (`npm run gcp:deploy`)